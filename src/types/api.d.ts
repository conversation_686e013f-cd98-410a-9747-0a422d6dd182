/// <reference types="types" />

declare namespace API {
  /** 埋点上报 */
  export interface EventTrackingParams {
    accessPath: string
    browser: string
    channel: string
    device: string
    eid: string
    eventType: string
    extend1: string
    extra: string
    jv: string
    language: string
    os: string
    pageUrl: string
    pageX: string
    pageY: string
    pv: string
    referer: string
    remark: string
    screen: string
    time: string
    vid: string
    [property: string]: any
  }

  /**
   * 配置类型
   * @property cfg_app_page_base_info - 基本信息页配置
   * @property cfg_app_page_contact_relation - 联系人页面配置
   * @property customer_service_tel - 客服电话及企微
   */
  export type ConfigType = 'cfg_app_page_base_info' | 'cfg_app_page_contact_relation' | 'customer_service_tel'

  /**
   * 协议类型代码
   * @property P01 - 登录隐私保护政策
   * @property P02 - 隐私政策
   * @property P03 - 支付条款
   * @property P04 - 会员规则
   */
  export type AgreementType = 'P01' | 'P02' | 'P03' | 'P04' | 'P20'

  // 用户相关接口
  namespace User {
    // 绑定银行卡参数
    export interface BankCardParams {
      /**
       * 银行卡号
       */
      card_no: string
      /**
       * 身份证号
       */
      idno: string
      /**
       * 手机号
       */
      phone: string
      /**
       * 姓名
       */
      realname: string
      [property: string]: any
    }

    // 添加地址参数
    interface AddressParams {
      /**
       * 城市
       */
      city: string
      /**
       * 详细地址
       */
      detail: string
      /**
       * 区县
       */
      district: string
      /**
       * 是否设置默认地址
       */
      is_default: number
      /**
       * 收货人手机号
       */
      phone: string
      /**
       * 省份
       */
      province: string
      /**
       * 收货人姓名
       */
      receiver: string
      [property: string]: any
    }

    interface LoginParams {
      username: string
      password: string
    }

    interface LoginResponse {
      token: string
      userInfo: {
        id: string
        name: string
        avatar?: string
      }
    }

    // 手机验证码登录参数
    interface PhoneLoginParams {
      phone: string
      code: string
      source?: 'app' | 'h5'
    }

    // 登录响应
    interface PhoneLoginResponse {
      refreshToken: string
      accessToken: string
      userInfo?: {
        id: string
        name: string
        phone: string
        avatar?: string
      }
    }

    // 获取验证码参数
    interface CaptchaParams {
      phone: string
      type: 'login' | 'logout'
    }

    // 获取验证码响应
    interface CaptchaResponse {
      success: boolean
      message?: string
    }
  }

  // 商品相关接口
  namespace Goods {
    interface ListParams {
      /**
       * 商品分类id
       */
      category_id?: number
      /**
       * 是否热门
       */
      is_hot?: number
      /**
       * 商品分类级别
       */
      level?: number
      /**
       * 排序方式：默认：DESC
       */
      order?: string
      page?: number
      size?: number
      /**
       * 排序字段，默认：created_at
       */
      sort?: string
      [property: string]: any
    }

    interface GoodsItem {
      id: string
      title: string
      price: number
      thumbnail: string
    }

    type ListResponse = {
      total: number
      list: GoodsItem[]
    }
  }

  // 购物车相关接口
  namespace Cart {
    // 添加购物车参数
    interface AddToCartParams {
      product_id: string | number
      quantity: number
    }

    // 更新购物车数量参数
    interface UpdateCartParams {
      cart_id: string | number
      quantity: number
    }

    // 删除购物车参数
    interface RemoveFromCartParams {
      cart_ids: Array<string | number>
    }

    // 购物车商品项
    interface CartItem {
      id: string | number
      product_id: string | number
      name: string
      price: number
      quantity: number
      image: string
      selected: boolean
      [key: string]: any
    }

    // 购物车列表响应
    interface CartListResponse {
      items: CartItem[]
      total_price: number
      total_quantity: number
    }

    // 购物车数量响应
    interface CartCountResponse {
      count: number
    }
  }
}
