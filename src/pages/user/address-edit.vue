<route lang="json5" type="page">
{
  layout: 'theme',
  style: {
    navigationStyle: 'default'
  }
}
</route>

<template>
  <view class="bg-[#F8F8F8]">
    <view class="px-30rpx py-30rpx text-28rpx text-[#222]">
      <!-- 表单区域 -->
      <view class="mb-20rpx rounded-20rpx bg-white overflow-hidden">
        <wd-cell-group>
          <!-- 姓名 -->
          <wd-input
            v-model="formData.receiver"
            label="姓名"
            label-width="172rpx"
            placeholder="请输入收货人姓名"
            clearable
            custom-class="py-6rpx"
          />

          <!-- 手机号 -->
          <wd-input
            v-model="formData.phone"
            label="手机号"
            label-width="172rpx"
            placeholder="请输入手机号"
            type="number"
            :maxlength="11"
            clearable
            custom-class="py-6rpx"
          />

          <!-- 所在地区 - 三级联动选择器 -->
          <wd-picker
            v-model="regionValue"
            :columns="regionColumns"
            label="所在地区"
            label-width="172rpx"
            placeholder="请选择所在地区"
            :column-change="onChangeDistrict"
            :display-format="displayFormat"
            custom-class="py-6rpx"
            @confirm="confirmRegion"
          />

          <!-- 详细地址 -->
          <wd-input
            v-model="formData.detail"
            label="详细地址"
            label-width="172rpx"
            placeholder="请输入详细地址，如街道、小区、楼栋号、单元室等"
            clearable
            show-word-limit
            custom-class="py-6rpx"
          />
        </wd-cell-group>
      </view>

      <!-- 设为默认 -->
      <view class="ml-20rpx mt-60rpx flex items-center">
        <wd-checkbox checked-color="var(--primary-color)" v-model="formData.is_default" />
        <text class="text-28rpx text-[#333]">设为默认收货地址</text>
      </view>

      <!-- 保存按钮 -->
      <view class="mt-164rpx">
        <view
          v-if="fromPage === 'order'"
          class="m-(x-auto y-30rpx) lh-44rpx max-w-700rpx rounded-12rpx bg-#000 p-(x-30rpx y-5rpx) text-center text-26rpx text-white leading-44rpx op-60"
        >
          请确认正确收货地址，确认订单后该地址不可修改！
        </view>
        <wd-button class="!btn-primary" :loading="loading" @click="saveAddress">保存</wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { addUserAddressApi, getUserAddressDetailApi, updateUserAddressApi } from '@/api'
import districtData from '@/static/area.js'

// 表单数据
const formData = reactive({
  id: '',
  receiver: '', // 姓名
  phone: '',
  province: '',
  city: '',
  district: '',
  detail: '', // 详细地址
  is_default: 0 // 设为默认 0/1
})

// 加载状态
const loading = ref(false)

// 选择器默认值 - [省, 市, 区]
const regionValue = ref(['110000', '110100', '110102'])

/**
 * 初始化三级联动的列数据
 * 第一列：省份列表
 * 第二列：根据第一列第一项决定的市级列表
 * 第三列：根据第二列第一项决定的区级列表
 */
const regionColumns = ref([
  districtData['0'], // 省级数据
  districtData[districtData['0'][0].value], // 市级数据，取第一个省的下级市
  districtData[districtData[districtData['0'][0].value][0].value] // 区级数据，取第一个市的下级区
])

/**
 * 根据省市区名称设置选择器的默认值
 * @param province 省份名称
 * @param city 城市名称
 * @param district 区县名称
 */
const setRegionValueByName = (province: string, city: string, district: string) => {
  console.log(`设置地区选择器默认值: ${province} ${city} ${district}`)

  const provinces = districtData['0']

  // 智能匹配省份
  let provinceItem = provinces.find(item => item.label === province)
  if (!provinceItem) {
    // 尝试去掉"省"、"市"、"自治区"等后缀
    const cleanProvince = province.replace(/[省市区县]$/, '')
    provinceItem = provinces.find(
      item =>
        item.label === cleanProvince ||
        item.label.includes(cleanProvince) ||
        cleanProvince.includes(item.label.replace(/[省市区县]$/, ''))
    )
  }

  if (!provinceItem) {
    console.warn(`未找到省份: ${province}，可用省份:`, provinces.map(p => p.label).slice(0, 10))
    return
  }

  const cities = districtData[provinceItem.value]

  // 智能匹配城市
  let cityItem = cities.find(item => item.label === city)
  if (!cityItem) {
    // 尝试去掉"市"后缀
    const cleanCity = city.replace(/[市]$/, '')
    cityItem = cities.find(
      item =>
        item.label === cleanCity ||
        item.label.includes(cleanCity) ||
        cleanCity.includes(item.label.replace(/[市]$/, ''))
    )
  }

  if (!cityItem) {
    console.warn(
      `未找到城市: ${city}，省份 ${provinceItem.label} 下的城市:`,
      cities.map(c => c.label)
    )
    return
  }

  const districts = districtData[cityItem.value]

  // 智能匹配区县
  let districtItem = districts.find(item => item.label === district)
  if (!districtItem) {
    // 尝试去掉"区"、"县"后缀
    const cleanDistrict = district.replace(/[区县]$/, '')
    districtItem = districts.find(
      item =>
        item.label === cleanDistrict ||
        item.label.includes(cleanDistrict) ||
        cleanDistrict.includes(item.label.replace(/[区县]$/, ''))
    )
  }

  if (!districtItem) {
    console.warn(
      `未找到区县: ${district}，城市 ${cityItem.label} 下的区县:`,
      districts.map(d => d.label)
    )
    return
  }

  // 更新选择器值
  regionValue.value = [provinceItem.value, cityItem.value, districtItem.value]

  // 更新列数据
  regionColumns.value = [provinces, cities, districts]

  console.log(`地区选择器设置成功:`, regionValue.value)
}

/**
 * 处理地区选择器的列变化事件
 * @param pickerView 选择器实例，用于设置列数据
 * @param value 当前选择的所有列的值
 * @param columnIndex 当前变化的列索引
 * @param resolve 完成回调函数
 */
const onChangeDistrict = (pickerView, value, columnIndex, resolve) => {
  const item = value[columnIndex]

  // 如果是省份列变化
  if (columnIndex === 0) {
    // 更新市级列表
    pickerView.setColumnData(1, districtData[item.value])
    // 更新区级列表，取第一个市的下级区
    pickerView.setColumnData(2, districtData[districtData[item.value][0].value])
  }
  // 如果是市级列变化
  else if (columnIndex === 1) {
    // 更新区级列表
    pickerView.setColumnData(2, districtData[item.value])
  }

  resolve() // 完成列变化处理
}

/**
 * 显示格式化函数，将选中的项转为显示文本
 * @param items 选中的项
 * @return 格式化后的显示文本
 */
const displayFormat = items => {
  return items
    .map(item => {
      return item.label
    })
    .join(' ')
}

/**
 * 确认地区选择事件处理函数
 * @param event 事件对象，包含selectedValue和selectedLabel
 */
const confirmRegion = (event: { selectedItems: { label: string; value: string }[]; value: string[] }) => {
  console.log(`event -->`, event)
  const items = event?.selectedItems || []
  if (Array.isArray(items) && items.length >= 3) {
    formData.province = items[0].label
    formData.city = items[1].label
    formData.district = items[2].label
  } else {
    formData.province = ''
    formData.city = ''
    formData.district = ''
  }
  regionValue.value = event.value

  console.log(`regionValue -->`, regionValue.value)
}

// 切换默认地址状态
const toggleDefault = () => {
  formData.is_default = formData.is_default ? 0 : 1
}

/**
 * 保存地址信息
 * 验证表单、生成完整地址并提交数据
 */
const saveAddress = async () => {
  if (formData.receiver.trim() === '') {
    uni.showToast({ title: '请输入收货人姓名', icon: 'none' })
    return
  }
  if (!/^1\d{10}$/.test(formData.phone)) {
    uni.showToast({ title: '请输入正确的手机号', icon: 'none' })
    return
  }
  if (formData.province === '' || formData.city === '' || formData.district === '') {
    uni.showToast({ title: '请选择所在地区', icon: 'none' })
    return
  }
  if (formData.detail.trim() === '') {
    uni.showToast({ title: '请输入详细地址', icon: 'none' })
    return
  }

  loading.value = true

  console.log(`formData -->`, formData)

  formData.is_default = formData.is_default ? 1 : 0

  let api
  if (formData.id) {
    api = updateUserAddressApi
  } else {
    delete formData.id
    api = addUserAddressApi
  }

  let [res, err] = await api(formData)
  if (err) {
    loading.value = false
    Utils.toast({ type: 'error', msg: err.data.message })
    return
  }
  Utils.toast({ type: 'success', msg: res.message || '地址添加成功' })
  uni.$emit('refreshAddressList') // 新增：通知列表页刷新
  uni.navigateBack()
  loading.value = false
}

const fromPage = ref('')
const editAddressInfo = ref({})

// 页面加载
onLoad((options: any) => {
  // 有id证明是编辑页面
  if (options.id) {
    // 获取编辑地址信息详情
    getUserAddressDetailApi(options.id)
      .unwrap()
      .then(res => {
        console.log(`res -->`, res?.data.address)
        Object.keys(formData).forEach(key => {
          if (res.data.address.hasOwnProperty(key)) {
            formData[key] = res.data.address[key]
          }
        })

        // 根据接口返回的省市区名称设置选择器默认值
        const address = res.data.address
        console.log('接口返回的地址数据:', {
          province: address.province,
          city: address.city,
          district: address.district
        })

        if (address.province && address.city && address.district) {
          setRegionValueByName(address.province, address.city, address.district)
        } else {
          console.warn('地址数据不完整:', address)
        }

        console.log(`formData -->`, formData)
      })
  }

  if (options.from) {
    fromPage.value = options.from
  }
})

onMounted(() => {
  if (fromPage.value === 'edit') {
    uni.setNavigationBarTitle({
      title: '编辑地址'
    })
  } else {
    uni.setNavigationBarTitle({
      title: '新增地址'
    })
  }
})
</script>

<style>
.region-value {
  font-size: 28rpx;
  color: #222;
}

.region-placeholder {
  font-size: 28rpx;
  color: #999;
}
</style>
