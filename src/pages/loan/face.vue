<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '人脸识别',
    navigationStyle: 'default',
    'app-plus': {
      scrollIndicator: 'none'
    }
  }
}
</route>

<template>
  <view>
    <view></view>
  </view>
</template>

<script setup lang="ts">
import { getFaceUrlApi } from '@/api/loan'
import { onLoad } from '@dcloudio/uni-app'

const faceUrl = ref('')

// 获取人脸识别地址
getFaceUrlApi()
  .unwrap()
  .then(res => {
    faceUrl.value = res.data?.auth_url
    uni.navigateTo({ url: `/pages/common/webview/index?url=${faceUrl.value}&title=人脸识别` })
  })
  .catch(err => {
    Utils.toast({ type: 'error', msg: err.data.message || '获取人脸识别地址失败' })
  })
</script>

<style scoped lang="scss"></style>
