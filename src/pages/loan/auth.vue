<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '身份认证',
    navigationStyle: 'default',
    'app-plus': {
      scrollIndicator: 'none'
    }
  }
}
</route>

<template>
  <view class="page-container flex flex-col pb-120rpx">
    <view class="p-30rpx">
      <view class="text-34rpx">
        请确保本人认证 预估最高可得
        <text class="text-#FF8D1A">50000元</text>
      </view>
      <view class="m-(t-10rpx b-70rpx) text-(#666 24rpx)">用于身份验证及借款评估，全程保证隐私安全</view>
      <view class="text-(#666 24rpx) mb-28rpx">请上传您的身份证原件，确保照片清晰、四角完整</view>

      <view class="flex justify-between mt-30rpx">
        <view class="w-50% h-220rpx rounded-lg" @click="onUpload('Front')">
          <image
            v-if="!formData.idCardFrontPic"
            class="w-full h-full"
            src="@/static/images/loan/front.png"
            mode="aspectFit"
          ></image>
          <image v-else :src="formData.idCardFrontPic" class="w-95% h-220rpx rounded-lg" mode="aspectFill"></image>
        </view>
        <view class="w-50% h-220rpx rounded-lg" @click="onUpload('Back')">
          <image
            v-if="!formData.idCardBackPic"
            class="w-full h-full"
            src="@/static/images/loan/contrary.png"
            mode="aspectFit"
          ></image>
          <image v-else :src="formData.idCardBackPic" class="w-95% h-220rpx rounded-lg" mode="aspectFill"></image>
        </view>
      </view>

      <!-- 只有在OCR识别完成后才显示这个提示 -->
      <view
        class="text-(26rpx #666) mt-4"
        v-if="formData.idCardFrontPic && formData.idCardBackPic && formData.realName"
      >
        请确认以下信息是否正确，可手动修改
      </view>

      <!-- 身份证识别信息 - 只有在两张图片上传并识别完成后才显示 -->
      <wd-cell-group
        v-if="formData.idCardFrontPic && formData.idCardBackPic && formData.realName"
        border
        custom-class="real-name bg-white rounded-lg overflow-hidden mt-4"
      >
        <wd-cell title="真实姓名" title-width="150rpx">
          <wd-input v-model="formData.realName" placeholder="请输入内容" no-border />
        </wd-cell>
        <wd-cell title="身份证号" title-width="150rpx">
          <wd-input v-model="formData.idCardNumber" placeholder="请输入内容" :maxlength="18" no-border />
        </wd-cell>
        <wd-cell title="有效期" title-width="150rpx">
          <wd-datetime-picker
            v-model="formData.idCardExpirationDate"
            placeholder="请选择日期"
            value-format="yyyy-MM-dd"
            :min-date="minDateTimestamp"
            :max-date="maxDateTimestamp"
            :display-format="formatDate"
            use-label-slot
          >
            <wd-input
              slot="trigger"
              v-model="formData.idCardExpirationDate"
              placeholder="请选择日期"
              no-border
              readonly
            />
          </wd-datetime-picker>
        </wd-cell>
      </wd-cell-group>

      <!-- 加载中状态 - 两张照片上传后但OCR识别中 -->
      <view
        v-if="formData.idCardFrontPic && formData.idCardBackPic && !formData.realName"
        class="bg-white rounded-lg mt-4 p-4 mb-120rpx flex flex-col items-center justify-center"
      >
        <view class="text-sm text-gray-800 mb-4">正在识别身份证信息...</view>
        <wd-loading color="#FE3635" />
      </view>

      <!-- 温馨提示 - 只有在OCR识别完成后才显示 -->
      <view
        v-else-if="formData.idCardFrontPic && formData.idCardBackPic && formData.realName"
        class="bg-white rounded-lg mt-4 p-4 mb-120rpx"
      >
        <view class="font-normal text-sm text-gray-800 mb-4">温馨提示:</view>
        <view class="text-xs text-gray-500 mb-3">
          上传身份证正反面进行实名认证是为了对您的基本信息，确保本人使用，防止财产诈骗。
        </view>
        <view class="text-xs text-gray-500 mb-3">请上传本人身份证照片，非本人信息无法通过审核。</view>
        <view class="text-xs text-gray-500 mb-3">确保证件边框完整，文字清晰可见。</view>
        <view class="text-xs text-gray-500">可现场拍摄或从手机相册获取。</view>
      </view>
    </view>

    <!-- 修改footer部分 -->
    <!-- footer -->
    <view class="footer-fixed">
      <view class="p-30rpx">
        <view class="mb-3">
          <wd-checkbox v-model="checked" checked-color="#FE3635" custom-color="#FE3635">
            <text class="text-xs text-gray-500">我已阅读并同意</text>
            <text
              v-for="item in agreementList"
              :key="item.id"
              class="text-xs text-primary"
              @click.stop="openAgreement(item)"
            >
              {{ item.name }}
            </text>
          </wd-checkbox>
        </view>

        <wd-button block @click="onSubmit" custom-class="btn-primary !h-72rpx" :disabled="!isFormValid">
          下一步
        </wd-button>

        <view class="text-(22rpx #7A7A7A center) mt-20rpx pb-[var(--safe-area-inset-bottom)]">
          平台承诺保证您的信息安全
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getAgreementApi, submitRealNameApi, getFaceUrlApi, recognizeIdCardApi } from '@/api'
import { useUserStore } from '@/store'

defineOptions({
  name: 'Auth'
})

// 表单数据
const formData = ref({
  idCardFrontPic: '',
  idCardBackPic: '',
  realName: '',
  idCardNumber: '',
  idCardExpirationDate: ''
})

// 日期限制 - 转换为时间戳
const minDateTimestamp = ref(new Date('2010-01-01').getTime())
const maxDateTimestamp = ref(new Date('2050-12-31').getTime())

// 日期格式化函数
const formatDate = date => {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 协议勾选
const checked = ref(false)

// 协议列表
const agreementList = ref([])

getAgreementApi({ type: 'P01' })
  .unwrap()
  .then(res => {
    agreementList.value = res.data
  })

// 打开协议
const openAgreement = (item: any) => {
  uni.navigateTo({
    url: `/pages/common/webview/index?title=${item.name}&url=${item.link}`
  })
}

// 上传身份证图片
const onUpload = (type: string) => {
  uni.chooseImage({
    count: 1,
    sizeType: ['original', 'compressed'],
    sourceType: ['album', 'camera'],
    success: async res => {
      try {
        const store = useUserStore()
        const filePath = res.tempFilePaths[0]

        uni.uploadFile({
          url: `${import.meta.env.VITE_SERVER_BASEURL}/credit/idcard/upload`,
          filePath: filePath,
          name: 'file',
          header: {
            channel: import.meta.env.VITE_APP_DEFAULT_CHANNEL,
            Authorization: `Bearer ${store.userInfo?.accessToken}`
          },
          success: res => {
            console.log('上传成功:', res)
            try {
              const data = JSON.parse(res.data)
              console.log(`data -->`, data)

              // 处理上传成功逻辑
              if (type === 'Front') {
                formData.value.idCardFrontPic = data.data?.url
              } else {
                formData.value.idCardBackPic = data.data?.url
              }

              if (formData.value.idCardFrontPic && formData.value.idCardBackPic) {
                ocrRecognition()
              }

              // Utils.toast({ type: 'success', msg: '上传成功' })
            } catch (error) {
              console.error('解析响应失败:', error)
            }
          },
          fail: (error: any) => {
            console.error('上传失败:', error)
            Utils.toast({ type: 'error', msg: error?.data?.message || '上传失败' })
          }
        })
      } catch (error) {
        console.error('文件处理失败:', error)
      }
    }
  })
}

// OCR识别
const ocrRecognition = async () => {
  const [res, err] = await recognizeIdCardApi({
    front_pic: formData.value.idCardFrontPic,
    back_pic: formData.value.idCardBackPic
  })
  if (err) {
    Utils.toast({ type: 'error', msg: err.data.message || '识别失败' })
    return
  }
  formData.value.realName = res.data.info.name
  formData.value.idCardNumber = res.data.info.idno
  formData.value.idCardExpirationDate = res.data.info.expire
}

// 判断表单是否有效（用于禁用/启用下一步按钮）
const isFormValid = computed(() => {
  return (
    checked.value &&
    formData.value.idCardFrontPic &&
    formData.value.idCardBackPic &&
    formData.value.realName &&
    formData.value.idCardNumber &&
    formData.value.idCardExpirationDate
  )
})

// 提交实名认证
const onSubmit = async () => {
  if (!checkInfo()) return

  Utils.loading({ msg: '提交中' })
  const [res, err] = await submitRealNameApi({
    name: formData.value.realName,
    idno: formData.value.idCardNumber
  })
  if (err) {
    Utils.closeLoading()
    Utils.toast({ type: 'error', msg: err.data?.message || '提交失败' })
    return
  }

  // uni.navigateTo({ url: '/pages/loan/face' })
  const [faceData, faceErr] = await getFaceUrlApi()
  if (faceErr) {
    Utils.closeLoading()
    Utils.toast({ type: 'error', msg: faceErr.data?.message || '获取人脸识别地址失败' })
    return
  }
  uni.navigateTo({
    url: `/pages/common/webview/index?url=${faceData.data?.auth_url}&title=人脸识别`,
    success: () => {
      Utils.closeLoading()
    }
  })
}

// 验证信息
const checkInfo = (): boolean => {
  if (!(formData.value.idCardFrontPic && formData.value.idCardBackPic)) {
    uni.showToast({ icon: 'none', title: '请上传身份证照片' })
    return false
  }

  if (!formData.value.realName || formData.value.realName.length < 2) {
    uni.showToast({ icon: 'none', title: '请输入正确的姓名' })
    return false
  }

  if (
    !formData.value.idCardNumber ||
    !/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dXx]$/.test(formData.value.idCardNumber)
  ) {
    uni.showToast({ icon: 'none', title: '请输入正确的身份证号码' })
    return false
  }

  if (!formData.value.idCardExpirationDate) {
    uni.showToast({ icon: 'none', title: '请选择身份证有效期' })
    return false
  }

  if (!checked.value) {
    uni.showToast({ icon: 'none', title: '请阅读并同意协议' })
    return false
  }

  return true
}
</script>

<style lang="scss" scoped>
.card-item {
  position: relative;
  display: flex;

  .title {
    width: 160rpx;
    line-height: 104rpx;
  }

  :deep(.uni-easyinput__content) {
    height: 100%;
  }
}

.uni-input {
  display: flex;
  align-items: center;
  height: 104rpx;
  font-size: 14px;
}

.card-input {
  flex: 1;
}

:deep(.wd-input__inner) {
  height: 104rpx;
  font-size: 28rpx;
}

:deep(.wd-datetime-picker) {
  flex: 1;
  width: 100%;
}

.real-name {
  :deep(.wd-cell__wrapper) {
    padding: 30rpx 0 !important;
  }
}

.footer-fixed {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>
