/**
 * 公共接口
 */
import http from '@/http/requestWrapper'

/** 埋点上报 */
export const sendTrackingApi = (data: API.EventTrackingParams) => {
  return http.post('/common/eventTracking', { data })
}

/** 获取协议 */
export const getAgreementApi = (data: { type: API.AgreementType }) => {
  return http.get('/common/getProtocol', { data })
}

/** 获取配置 */
export const getConfigApi = (data: { key: API.ConfigType }) => {
  return http.get('/common/getConfig', { data })
}
