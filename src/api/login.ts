import http from '@/http/requestWrapper'
import { useUserStore } from '@/store'

/* 登录 获取 accessToken */
export const loginApi = async (data: API.User.PhoneLoginParams) => {
  const res = await http.post('/user/login', { data }).unwrap()
  const store = useUserStore()
  store.setUserInfo({
    refreshToken: res.data.refreshToken,
    accessToken: res.data.token,
    phone: data?.phone
  })
}

/* 拿 refreshToken 换取 accessToken 与 新 refreshToken */
/* 即刷新 accessToken */
export const refreshTokenApi = () => {
  const store = useUserStore()
  const { refreshToken } = store.userInfo || {}
  return http
    .get('/refresh', {
      data: {
        refreshToken: refreshToken
      }
    })
    .then(
      ([res, _]) => {
        if (res) {
          store.setUserInfo({
            refreshToken: res.data.refreshToken,
            accessToken: res.data.accessToken
          })
        }
        return [res, null]
      },
      e => [null, e]
    )
}

/* 获取用户信息 */
export const getListApi = () => {
  return http.get('/userInfo')
}

/** 获取短信验证码 */
export const getCaptchaApi = (data: API.User.CaptchaParams) => {
  return http.post('/common/captcha', { data })
}

/** 退出登录 */
export const logoutApi = async (data?: any) => {
  const [res, err] = await http.post('/user/logout', { data: {} })
  if (err) {
    Utils.toast({ type: 'error', msg: err.data?.message || '退出登录失败' })
    return
  }
  const store = useUserStore()
  store.clearUserInfo()
  uni.clearStorageSync()
  uni.reLaunch({ url: '/pages/common/login/index' })
}
