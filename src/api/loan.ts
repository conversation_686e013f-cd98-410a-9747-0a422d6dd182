import http from '@/http/requestWrapper'
import { useUserStore } from '@/store'

/** 身份证上传 */
export const uploadIdCardApi = (data: string) => {
  const store = useUserStore()

  return http.post(`/credit/idcard/upload`, {
    data,
    header: {
      'Content-Type': 'multipart/form-data;boundary=--------------------------810883379640308914500262',
      channel: 'ceshi',
      Authorization: `Bearer ${store.userInfo?.accessToken}`
    }
  })
}
/** 身份证OCR识别 */
export const recognizeIdCardApi = (data: { back_pic: string; front_pic: string }) => {
  return http.post(`/credit/recognizeIdcard`, { data })
}

/** 实名信息提交 */
export const submitRealNameApi = (data: { name: string; idno: string }) => {
  return http.post(`/credit/idcard`, { data })
}

/** 获取人脸识别地址 */
export const getFaceUrlApi = () => {
  return http.get(`/credit/getAdvFaceH5`)
}
