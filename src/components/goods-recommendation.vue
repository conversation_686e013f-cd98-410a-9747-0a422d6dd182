<template>
  <view class="goods-recommendation">
    <!-- 标题区域：使用showTitle控制显示，支持自定义插槽 -->
    <template v-if="showTitle">
      <slot name="title">
        <shop-title :title="title"></shop-title>
      </slot>
    </template>

    <z-paging
      ref="pagingRef"
      v-model="goodsList"
      @query="onGoodsPagingQuery"
      use-page-scroll
      :fixed="false"
      safe-area-inset-bottom
      :refresher-enabled="false"
      class="mt-5 pb-20rpx"
      v-bind="$attrs"
    >
      <view class="grid grid-cols-2 gap-24rpx" style="min-height: 100rpx">
        <goods-item
          v-for="item in goodsList"
          :key="item.id"
          :goods="item"
          @click="handleGoodsItemClick(item)"
          @goods-add-cart="handleGoodsAddCart(item)"
        />
      </view>

      <!-- 透传所有插槽 -->
      <template v-for="(_, name) in slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData"></slot>
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, useSlots } from 'vue'
import type { PropType } from 'vue'
import GoodsItem from './goods-item.vue'
import { getGoodsRecommendListApi } from '@/api'
import { onPageScroll, onReachBottom } from '@dcloudio/uni-app'

defineOptions({
  name: 'GoodsRecommendation',
  // 指定透传属性和事件到z-paging组件
  inheritAttrs: false
})

// 获取传递给组件的所有插槽
const slots = useSlots()

// Props 定义，只包含组件自身特有的属性
const props = defineProps({
  // 标题文本
  title: {
    type: String,
    default: '商品推荐'
  },
  // 是否显示标题
  showTitle: {
    type: Boolean,
    default: true
  },
  customApi: {
    type: Function as PropType<(page: number, size: number) => Promise<any>>,
    default: null
  },
  // 额外的请求参数
  requestParams: {
    type: Object,
    default: () => ({})
  }
})

// 事件定义
const emit = defineEmits(['goods-click', 'goods-add-cart'])

// 商品列表数据
const goodsList = ref<any[]>([])
const pagingRef = ref()

onPageScroll(e => {
  pagingRef.value?.updatePageScrollTop(e.scrollTop)
})
onReachBottom(() => {
  pagingRef.value?.pageReachBottom()
})

// 公开的方法和属性
defineExpose({
  pagingRef,
  updatePageScrollTop: (scrollTop: number) => {
    pagingRef.value?.updatePageScrollTop(scrollTop)
  },
  pageReachBottom: () => {
    pagingRef.value?.pageReachBottom()
  },
  refresh: () => {
    pagingRef.value?.reload()
  },
  // 提供手动更新请求参数并刷新列表的方法
  updateRequestParams: (newParams: Record<string, any>) => {
    // 这里仅触发刷新，实际参数需要通过props传入
    pagingRef.value?.reload()
  }
})

// 商品列表查询
const onGoodsPagingQuery = async (pageNo: number, pageSize: number) => {
  try {
    // 使用自定义API或默认API
    if (props.customApi) {
      const data = await props.customApi(pageNo, pageSize)
      pagingRef.value.complete(data || [])
      return
    }

    // 合并分页参数和自定义请求参数
    const requestParams = {
      page: pageNo,
      size: pageSize,
      ...props.requestParams
    }

    const [res] = await getGoodsRecommendListApi(requestParams)
    const list = res?.data || []
    pagingRef.value.complete(list)
  } catch (error) {
    console.error('获取商品推荐列表失败', error)
    pagingRef.value.complete(false)
  }
}

// 商品点击事件处理
const handleGoodsItemClick = (goods: any) => {
  // 先触发事件，让外部有机会先处理
  emit('goods-click', goods)

  // 默认行为：尝试跳转到商品详情页
  // 如果外部事件处理已经执行了跳转，这里会静默失败
  uni.navigateTo({
    url: `/pages/shop/shop-detail?id=${goods.id}`,
    fail: () => {
      // 导航失败，可能是因为外部事件处理已经触发了跳转
      // 静默失败，不做额外处理
    }
  })
}

// 处理商品加入购物车事件
function handleGoodsAddCart(goods: any) {
  emit('goods-add-cart', goods)
}
</script>

<style lang="scss" scoped>
.goods-recommendation {
  width: 100%;
}
</style>
